{"name": "h5-upload-app", "version": "1.0.0", "description": "uniapp + vue3 图片上传应用", "main": "main.js", "scripts": {"dev:h5": "uni", "dev": "vite", "build:h5": "uni build --platform h5", "serve": "uni serve", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-i18n": "3.0.0-4020920240930001", "@dcloudio/uni-stacktracey": "3.0.0-4020920240930001", "fabric": "^4.6.0", "tui-color-picker": "^2.2.8", "tui-image-editor": "^3.14.3", "vue": "^3.4.0", "vue-router": "^4.0.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@types/node": "^20.0.0", "@vue/tsconfig": "^0.4.0", "sass": "^1.77.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "uni-app": {"scripts": {}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}